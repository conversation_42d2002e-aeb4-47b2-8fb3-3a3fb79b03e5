#![no_std]
#![no_main]

use defmt::*;
use display_interface_spi::SPIInterface;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_time::{Duration, Timer};
use embedded_graphics::{
    image::{Image, ImageRaw},
    mono_font::{MonoTextStyleBuilder, ascii::FONT_6X10},
    pixelcolor::BinaryColor,
    prelude::*,
    text::{Baseline, Text},
};
use software_spi::{AsyncSoftwareSpi, AsyncSoftwareSpiDevice};
use ssd1306::{Ssd1306, Ssd1306Async, mode::BufferedGraphicsMode, prelude::*};
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Default::default());

    info!("启动异步软件SPI示例");

    // 配置GPIO引脚
    let sck = Output::new(p.PD14, Level::Low, Speed::VeryHigh);
    let mosi = Output::new(p.PD13, Level::Low, Speed::VeryHigh);
    let mut res = Output::new(p.PD12, Level::High, Speed::VeryHigh);
    let dc = Output::new(p.PD11, Level::Low, Speed::VeryHigh);
    let cs = Output::new(p.PA8, Level::High, Speed::High); // CS应该初始化为高电平

    // 执行硬件复位
    info!("执行OLED硬件复位");
    res.set_low();
    Timer::after_millis(10).await;
    res.set_high();
    Timer::after_millis(10).await;

    // 创建异步软件SPI实例，时钟频率约为500kHz
    let spi_bus = AsyncSoftwareSpi::new(
        sck,
        mosi,
        software_spi::Mode::MODE_0,
        Duration::from_micros(1),
    )
    .unwrap();

    let spi = AsyncSoftwareSpiDevice::new(spi_bus, cs).unwrap();

    let interface = SPIInterface::new(spi, dc);
    let mut display_spi = Ssd1306Async::new(interface, DisplaySize128x64, DisplayRotation::Rotate0)
        .into_buffered_graphics_mode();

    // 初始化显示器
    info!("初始化OLED显示器");
    match display_spi.init().await {
        Ok(_) => info!("OLED初始化成功"),
        Err(_) => {
            info!("OLED初始化失败");
            return;
        }
    }

    // 清空显示缓冲区
    display_spi.clear(BinaryColor::Off).unwrap();

    let text_style = MonoTextStyleBuilder::new()
        .font(&FONT_6X10)
        .text_color(BinaryColor::On)
        .build();

    Text::with_baseline("Hello world!", Point::zero(), text_style, Baseline::Top)
        .draw(&mut display_spi)
        .unwrap();

    Text::with_baseline("Hello Rust!", Point::new(0, 16), text_style, Baseline::Top)
        .draw(&mut display_spi)
        .unwrap();

    match display_spi.flush().await {
        Ok(_) => info!("ok"),
        _ => info!("er"),
    };
    loop {
        Timer::after_millis(10).await;
    }
}
