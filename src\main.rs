#![no_std]
#![no_main]

use defmt::*;
use display_interface_spi::SPIInterface;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_time::{Duration, Timer};
use embedded_graphics::{
    mono_font::{MonoTextStyleBuilder, ascii::FONT_6X10},
    pixelcolor::BinaryColor,
    prelude::*,
    primitives::{PrimitiveStyle, Rectangle},
    text::{Baseline, Text},
};
use software_spi::{AsyncSoftwareSpi, AsyncSoftwareSpiDevice};
use ssd1306::{Ssd1306Async, prelude::*};
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Default::default());

    info!("启动异步软件SPI示例");

    // 配置GPIO引脚
    let sck = Output::new(p.PD14, Level::Low, Speed::VeryHigh);
    let mosi = Output::new(p.PD13, Level::Low, Speed::VeryHigh);
    let mut res = Output::new(p.PD12, Level::High, Speed::VeryHigh);
    let dc = Output::new(p.PD11, Level::Low, Speed::VeryHigh);
    // 虚拟CS引脚 - 虽然硬件上CS接地，但软件仍需要一个引脚来满足接口要求
    let dummy_cs = Output::new(p.PA8, Level::High, Speed::High);

    // 执行硬件复位
    info!("执行OLED硬件复位");
    res.set_low();
    Timer::after_millis(10).await;
    res.set_high();
    Timer::after_millis(10).await;

    // 创建异步软件SPI实例，降低时钟频率到约100kHz
    // 注意: 如果MODE_0不工作，可以尝试MODE_3
    let spi_bus = AsyncSoftwareSpi::new(
        sck,
        mosi,
        software_spi::Mode::MODE_0, // 可以尝试改为MODE_3
        Duration::from_micros(5),   // 大幅降低时钟频率，提高兼容性
    )
    .unwrap();
    info!("SPI总线创建成功，模式: MODE_0，延时: 5μs");

    // 创建SPI设备，虽然CS在硬件上接地，但这里仍需要提供一个引脚
    let spi_device = AsyncSoftwareSpiDevice::new(spi_bus, dummy_cs).unwrap();

    let interface = SPIInterface::new(spi_device, dc);
    let mut display_spi = Ssd1306Async::new(interface, DisplaySize128x64, DisplayRotation::Rotate0)
        .into_buffered_graphics_mode();

    // 初始化显示器
    info!("初始化OLED显示器");
    match display_spi.init().await {
        Ok(_) => info!("OLED初始化成功"),
        Err(_) => {
            info!("OLED初始化失败");
            return;
        }
    }

    // 清空显示缓冲区
    display_spi.clear(BinaryColor::Off).unwrap();
    info!("显示缓冲区已清空");

    // 测试1: 绘制一个简单的矩形
    info!("测试1: 绘制矩形");
    let rect_style = PrimitiveStyle::with_fill(BinaryColor::On);
    Rectangle::new(Point::new(10, 10), Size::new(20, 20))
        .into_styled(rect_style)
        .draw(&mut display_spi)
        .unwrap();

    match display_spi.flush().await {
        Ok(_) => info!("矩形绘制成功"),
        Err(_) => info!("矩形绘制失败"),
    }

    Timer::after_millis(2000).await; // 等待2秒观察

    // 测试2: 清空并绘制文本
    info!("测试2: 绘制文本");
    display_spi.clear(BinaryColor::Off).unwrap();

    let text_style = MonoTextStyleBuilder::new()
        .font(&FONT_6X10)
        .text_color(BinaryColor::On)
        .build();

    Text::with_baseline("Hello world!", Point::zero(), text_style, Baseline::Top)
        .draw(&mut display_spi)
        .unwrap();

    Text::with_baseline("Hello Rust!", Point::new(0, 16), text_style, Baseline::Top)
        .draw(&mut display_spi)
        .unwrap();

    match display_spi.flush().await {
        Ok(_) => info!("文本绘制成功"),
        Err(_) => info!("文本绘制失败"),
    }

    Timer::after_millis(2000).await; // 等待2秒观察

    // 测试3: 全屏填充测试
    info!("测试3: 全屏填充");
    display_spi.clear(BinaryColor::On).unwrap(); // 全屏点亮

    match display_spi.flush().await {
        Ok(_) => info!("全屏填充成功"),
        Err(_) => info!("全屏填充失败"),
    };
    loop {
        Timer::after_millis(10).await;
    }
}
