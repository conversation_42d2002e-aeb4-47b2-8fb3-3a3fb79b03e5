[package]
authors = ["Alddp <<EMAIL>>"]
edition = "2024"
readme = "README.md"
name = "f4_learn"
version = "0.1.0"
license = "MIT OR Apache-2.0"

[[bin]]
name = "f4_learn"
test = false
bench = false

[lib]
test = false
bench = false

[dependencies]
embassy-stm32 = { version = "0.2.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "stm32f407ve",
    "unstable-pac",
    "memory-x",
    "time-driver-tim4",
    "exti",
    "chrono",
] }
embassy-sync = { version = "0.7.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
] }
embassy-executor = { version = "0.8.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "arch-cortex-m",
    "executor-thread",
    "executor-interrupt",
    "defmt",
] }
embassy-time = { version = "0.4.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768",
] }
embassy-usb = { version = "0.5.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
] }
embassy-net = { version = "0.7.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "tcp",
    "dhcpv4",
    "medium-ethernet",
] }
embassy-net-wiznet = { version = "0.2.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
] }
embassy-futures = { version = "0.1.0", git = "https://github.com/embassy-rs/embassy.git" }

defmt = "1.0.1"
defmt-rtt = "1.0.0"

cortex-m = { version = "0.7.6", features = [
    "inline-asm",
    "critical-section-single-core",
] }
cortex-m-rt = "0.7.0"
embedded-hal = "1.0.0"
embedded-hal-bus = { version = "0.2.0", features = ["async"] }
embedded-io = { version = "0.6.0" }
embedded-io-async = { version = "0.6.1" }
panic-probe = { version = "1.0.0", features = ["print-defmt"] }
futures-util = { version = "0.3.30", default-features = false }
heapless = { version = "0.8", default-features = false }
critical-section = "1.1"
nb = "1.1.0"
embedded-storage = "0.3.1"
micromath = "2.0.0"
usbd-hid = "0.8.1"
static_cell = "2"
chrono = { version = "^0.4", default-features = false }

# ==================== add ==================== 
ssd1306 = {version = "0.10.0", features = ["async"]}
embedded-graphics = "0.8.1"
display-interface-spi = "0.5.0"
embedded-hal-async = "1.0.0"
software-spi = { path = "../../library/software-spi", features = ["async"] }


[profile.dev]
opt-level = "s"
lto = "off"
overflow-checks = true
panic = "unwind"

[profile.release]
opt-level = "s"
debug = true
codegen-units = 1
overflow-checks = false
lto = true
incremental = false
